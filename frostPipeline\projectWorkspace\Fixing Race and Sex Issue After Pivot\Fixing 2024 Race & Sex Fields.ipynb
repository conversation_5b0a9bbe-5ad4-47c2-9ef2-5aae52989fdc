import pandas as pd

%pip install openpyxl

pivotedData =pd.read_csv('FROST_Merged_Final_Data_Using_updated_2024_Data.csv')

pivotedData


raw2024Data = pd.read_excel('2024 Interim Raw Data.xlsx')

# Create a column, decedent<PERSON>ey, by concatenating '2009' with the index + 1
raw2024Data['decedentKey'] = '2024' + (raw2024Data.index + 1).astype(str)
#Convert decedentKey to int
raw2024Data['decedentKey'] = raw2024Data['decedentKey'].astype(int)

raw2024Data

pivotedData[pivotedData.Year == 2024].sort_values('decedentKey')

#merge pivotedData with raw2024Data on decedentKey
mergedData = pd.merge(pivotedData, raw2024Data, on='decedentKey', how='left',
                      suffixes=('', '_raw2024'))

mergedData

mergedData.Sex.value_counts(dropna=False)

mergedData.Race.value_counts(dropna=False)

validSexes2024Int = ['F', 'Female', 'M', 'Male']

#Capitalize the Sex values
mergedData['Sex'] = mergedData['Sex'].str.capitalize()

# If a row has an invalid Sex value, swap the Race and Sex Values
mergedData.loc[~mergedData.Sex.isin(validSexes2024Int), ['Race', 'Sex']] = mergedData.loc[~mergedData.Sex.isin(validSexes2024Int), ['Sex', 'Race_raw2024']].values

# Remove Gender Column if it exists
if 'Gender' in mergedData.columns:
    mergedData = mergedData.drop(columns=['Gender'])

#Rename sex column to gender
mergedData = mergedData.rename(columns={'Sex': 'Gender'})

#In the Gender Column, replace 'Female' with 'F' and 'Male' with 'M'
mergedData['Gender'] = mergedData['Gender'].replace({'Female': 'F', 'Male': 'M'})

mergedData.Gender.value_counts(dropna=False)

mergedData.Race.value_counts(dropna=False)

# Remove all columns with suffix '_raw2024'
mergedData = mergedData[[col for col in mergedData.columns if not col.endswith('_raw2024')]]

# Remove all columns that were not in the original pivotedData
mergedData = mergedData[pivotedData.columns]

mergedData

mergedData.Race.value_counts(dropna=False)

raceCleaningDict = {
        'W'                             :       'White',
        'B'                             :       'Black',
        'H'                             :       'Hispanic',
        'HISPANIC'                      :       'Hispanic',
        'O'                             :       'Other',
        'OTHER'                         :       'Other',
        'WHITE'                         :       'White',
        'ASIAN'                         :       'Asian',
        'BLACK'                         :       'Black',
        'I'                             :       'Other',
        'U'                             :       'White',
        'AI'                            :       'American Indian',
        'WH'                            :       'White',
        'UNKNOWN'                       :       'Unknown',
        'ASIAN OR PACIFIC ISLANDER'     :       'Asian Or Pacific Islander',
        'AMERICAN INDIAN'               :       'American Indian',
        'A'                             :       'Asian',
        'F'                             :       'Unknown',
        'P'                             :       'Unknown',
        'E'                             :       'Unknown',
        'BL'                            :       'Black',
        'W M'                           :       'Unknown',
        'N'                             :       'Unknown',
        'C'                             :       'Unknown',
        'W+'                            :       'Unknown',
        'Q'                             :       'Unknown',
        'WW'                            :       'Unknown',
        'M'                             :       'Unknown',
        'AA'                            :       'Black',
        'AFRICAN AMERICAN'              :       'Black',
        'NATIVE AMERICAN'               :       'American Indian',
        '`W'                            :       'Unknown',
        'CHINESE'                       :       'Chinese',
        'NA'                            :       'American Indian'
}

#reducedDF.Race = reducedDF.Race.str.upper().str.strip().replace(raceCleaningDict)
mergedData.Race = mergedData.Race.str.upper().str.strip().replace(raceCleaningDict)
#reducedDF[ ~ reducedDF.Race.isin(raceCleaningDict.values())].Race.value_counts()
mergedData.loc[ ~ mergedData.Race.isin(raceCleaningDict.values())]['Race'].value_counts()# = 'Unknown'
mergedData.Race.value_counts()

#Make the Race Column the first letter of RaceGroup
mergedData['Race'] = mergedData.RaceGroup.str[0]


#Rename Race to RaceGroup
mergedData = mergedData.rename(columns={'Race': 'RaceGroup'})

#Convert RaceGroup string
mergedData.RaceGroup = mergedData.RaceGroup.str.upper().str.strip()

#Make the Race Column the first letter of RaceGroup
mergedData['Race'] = mergedData.RaceGroup.str[0]

mergedData

mergedData.Race.value_counts(dropna=False).sort_index()

mergedData.RaceGroup.value_counts(dropna=False).sort_index()